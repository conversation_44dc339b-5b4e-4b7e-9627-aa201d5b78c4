package com.outbook.ksd.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "meeting")
data class Meeting(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,
    
    @Column(name = "name", nullable = false, length = 200)
    val name: String = "",
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: MeetingStatus = MeetingStatus.INACTIVE,
    
    @Column(name = "logo_url", length = 500)
    val logoUrl: String? = null,
    
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

enum class MeetingStatus {
    CURRENT,    // 当前会议
    INACTIVE    // 无效会议
}
