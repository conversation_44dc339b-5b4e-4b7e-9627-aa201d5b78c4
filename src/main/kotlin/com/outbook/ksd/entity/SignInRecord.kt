package com.outbook.ksd.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "sign_in_record")
data class SignInRecord(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "company_name", nullable = false, length = 200)
    val companyName: String = "",

    @Column(name = "name", nullable = false, length = 100)
    val name: String = "",

    @Column(name = "phone", nullable = false, length = 20)
    val phone: String = "",

    @Column(name = "position", nullable = false, length = 100)
    val position: String = "",

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "meeting_id")
    val meeting: Meeting? = null,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now()
)
