package com.outbook.ksd.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern

data class SignInRequest(
    @field:NotBlank(message = "公司名称不能为空")
    val companyName: String,
    
    @field:NotBlank(message = "姓名不能为空")
    val name: String,
    
    @field:NotBlank(message = "电话不能为空")
    @field:Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    val phone: String,
    
    @field:NotBlank(message = "职位不能为空")
    val position: String
)
