package com.outbook.ksd.dto

import com.outbook.ksd.entity.MeetingStatus
import jakarta.validation.constraints.NotBlank
import org.springframework.web.multipart.MultipartFile

data class MeetingRequest(
    @field:NotBlank(message = "会议名称不能为空")
    val name: String,

    val status: MeetingStatus = MeetingStatus.INACTIVE,

    val logoUrl: String? = null,

    val logoFile: MultipartFile? = null,

    val uploadType: String = "url" // "url" 或 "file"
)
