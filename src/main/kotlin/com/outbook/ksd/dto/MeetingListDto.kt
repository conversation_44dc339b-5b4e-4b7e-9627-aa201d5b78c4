package com.outbook.ksd.dto

import com.outbook.ksd.entity.Meeting
import com.outbook.ksd.entity.MeetingStatus
import java.time.LocalDateTime

data class MeetingListDto(
    val id: Long,
    val name: String,
    val status: MeetingStatus,
    val logoUrl: String?,
    val signInCount: Long,
    val createdAt: LocalDateTime
) {
    companion object {
        fun from(meeting: Meeting, signInCount: Long): MeetingListDto {
            return MeetingListDto(
                id = meeting.id!!,
                name = meeting.name,
                status = meeting.status,
                logoUrl = meeting.logoUrl,
                signInCount = signInCount,
                createdAt = meeting.createdAt
            )
        }
    }
}
