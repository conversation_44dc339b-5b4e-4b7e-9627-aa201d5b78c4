package com.outbook.ksd.service

import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class FileUploadService {
    
    companion object {
        private const val UPLOAD_DIR = "src/main/resources/static/uploads/meetings"
        private const val MAX_FILE_SIZE = 5 * 1024 * 1024L // 5MB
        private val ALLOWED_EXTENSIONS = setOf("jpg", "jpeg", "png")
        private val ALLOWED_MIME_TYPES = setOf("image/jpeg", "image/png")
    }
    
    init {
        // 确保上传目录存在
        createUploadDirectory()
    }
    
    fun uploadMeetingLogo(file: MultipartFile): String {
        // 验证文件
        validateFile(file)
        
        // 生成文件名
        val fileName = generateFileName(file.originalFilename ?: "logo")
        
        // 保存文件
        val filePath = saveFile(file, fileName)
        
        // 返回文件访问路径用于数据库存储
        return "/files/uploads/meetings/$fileName"
    }
    
    private fun validateFile(file: MultipartFile) {
        if (file.isEmpty) {
            throw IllegalArgumentException("文件不能为空")
        }
        
        if (file.size > MAX_FILE_SIZE) {
            throw IllegalArgumentException("文件大小不能超过5MB")
        }
        
        val contentType = file.contentType
        if (contentType == null || !ALLOWED_MIME_TYPES.contains(contentType.lowercase())) {
            throw IllegalArgumentException("只支持JPG、JPEG、PNG格式的图片")
        }
        
        val originalFilename = file.originalFilename ?: ""
        val extension = getFileExtension(originalFilename).lowercase()
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            throw IllegalArgumentException("只支持JPG、JPEG、PNG格式的图片")
        }
        
        // 验证文件头，确保是真正的图片文件
        validateImageHeader(file)
    }
    
    private fun validateImageHeader(file: MultipartFile) {
        try {
            val bytes = file.bytes
            if (bytes.size < 4) {
                throw IllegalArgumentException("无效的图片文件")
            }
            
            // 检查常见图片格式的文件头
            val header = bytes.take(4).map { it.toUByte().toInt() }
            
            val isJpeg = header[0] == 0xFF && header[1] == 0xD8
            val isPng = header[0] == 0x89 && header[1] == 0x50 && header[2] == 0x4E && header[3] == 0x47
            
            if (!isJpeg && !isPng) {
                throw IllegalArgumentException("文件格式无效，请上传真正的图片文件")
            }
        } catch (e: IOException) {
            throw IllegalArgumentException("文件读取失败")
        }
    }
    
    private fun generateFileName(originalFilename: String): String {
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
        val uuid = UUID.randomUUID().toString().substring(0, 8)
        val extension = getFileExtension(originalFilename)

        // 只使用英文字符和数字，避免中文字符导致的URL问题
        val cleanName = originalFilename.substringBeforeLast(".")
            .replace(Regex("[^a-zA-Z0-9]"), "_")
            .take(10)
            .ifEmpty { "logo" }

        return "meeting_logo_${timestamp}_${uuid}_${cleanName}.${extension}"
    }
    
    private fun getFileExtension(filename: String): String {
        return filename.substringAfterLast(".", "")
    }
    
    private fun saveFile(file: MultipartFile, fileName: String): Path {
        try {
            val uploadPath = Paths.get(UPLOAD_DIR)
            val filePath = uploadPath.resolve(fileName)
            
            Files.copy(file.inputStream, filePath, StandardCopyOption.REPLACE_EXISTING)
            return filePath
        } catch (e: IOException) {
            throw RuntimeException("文件保存失败: ${e.message}")
        }
    }
    
    private fun createUploadDirectory() {
        try {
            val uploadPath = Paths.get(UPLOAD_DIR)
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath)
            }
        } catch (e: IOException) {
            throw RuntimeException("无法创建上传目录: ${e.message}")
        }
    }
    
    fun deleteFile(relativePath: String): Boolean {
        return try {
            if (relativePath.startsWith("/files/uploads/meetings/")) {
                val fileName = relativePath.substringAfterLast("/")
                val filePath = Paths.get(UPLOAD_DIR, fileName)
                Files.deleteIfExists(filePath)
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
}
