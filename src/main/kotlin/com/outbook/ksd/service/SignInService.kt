package com.outbook.ksd.service

import com.outbook.ksd.dto.SignInRequest
import com.outbook.ksd.entity.SignInRecord
import com.outbook.ksd.repository.SignInRecordRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class SignInService(
    private val signInRecordRepository: SignInRecordRepository,
    private val meetingService: MeetingService
) {
    
    @Transactional
    fun signIn(request: SignInRequest): SignInRecord? {
        // 获取当前会议
        val currentMeeting = meetingService.getCurrentMeeting()
            ?: return null // 如果没有当前会议，返回null

        val record = SignInRecord(
            companyName = request.companyName,
            name = request.name,
            phone = request.phone,
            position = request.position,
            meeting = currentMeeting
        )
        return signInRecordRepository.save(record)
    }
    
    fun getAllSignInRecords(): List<SignInRecord> {
        return signInRecordRepository.findAllOrderByCreatedAtDesc()
    }

    fun getSignInRecordsByMeeting(meetingId: Long): List<SignInRecord> {
        return signInRecordRepository.findByMeetingIdOrderByCreatedAtDesc(meetingId)
    }

    fun getCurrentMeeting() = meetingService.getCurrentMeeting()
}
