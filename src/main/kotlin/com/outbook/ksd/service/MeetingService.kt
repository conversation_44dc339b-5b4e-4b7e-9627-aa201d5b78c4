package com.outbook.ksd.service

import com.outbook.ksd.dto.MeetingListDto
import com.outbook.ksd.dto.MeetingRequest
import com.outbook.ksd.entity.Meeting
import com.outbook.ksd.entity.MeetingStatus
import com.outbook.ksd.repository.MeetingRepository
import com.outbook.ksd.repository.SignInRecordRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class MeetingService(
    private val meetingRepository: MeetingRepository,
    private val signInRecordRepository: SignInRecordRepository
) {
    
    fun getCurrentMeeting(): Meeting? {
        return meetingRepository.findByStatus(MeetingStatus.CURRENT).orElse(null)
    }
    
    fun findById(id: Long): Meeting? {
        return meetingRepository.findById(id).orElse(null)
    }
    
    fun getAllMeetingsWithSignInCount(page: Int, size: Int): Page<MeetingListDto> {
        val pageable: Pageable = PageRequest.of(page, size)
        val meetingsPage = meetingRepository.findAllOrderByStatusAndCreatedAt(pageable)
        
        return meetingsPage.map { meeting ->
            val signInCount = signInRecordRepository.countByMeetingId(meeting.id!!)
            MeetingListDto.from(meeting, signInCount)
        }
    }
    
    @Transactional
    fun createMeeting(request: MeetingRequest): Meeting {
        // 如果新会议状态为CURRENT，需要将其他会议设为INACTIVE
        if (request.status == MeetingStatus.CURRENT) {
            setAllMeetingsInactive()
        }
        
        val meeting = Meeting(
            name = request.name,
            status = request.status,
            logoUrl = request.logoUrl,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
        
        return meetingRepository.save(meeting)
    }
    
    @Transactional
    fun setMeetingAsCurrent(meetingId: Long): Meeting? {
        val meeting = meetingRepository.findById(meetingId).orElse(null) ?: return null
        
        // 将所有会议设为INACTIVE
        setAllMeetingsInactive()
        
        // 将指定会议设为CURRENT
        val updatedMeeting = meeting.copy(
            status = MeetingStatus.CURRENT,
            updatedAt = LocalDateTime.now()
        )
        
        return meetingRepository.save(updatedMeeting)
    }
    
    private fun setAllMeetingsInactive() {
        val currentMeetings = meetingRepository.findAllOrderByStatusAndCreatedAt()
        currentMeetings.forEach { meeting ->
            if (meeting.status == MeetingStatus.CURRENT) {
                val inactiveMeeting = meeting.copy(
                    status = MeetingStatus.INACTIVE,
                    updatedAt = LocalDateTime.now()
                )
                meetingRepository.save(inactiveMeeting)
            }
        }
    }
}
