package com.outbook.ksd.service

import com.outbook.ksd.entity.AdminUser
import com.outbook.ksd.repository.AdminUserRepository
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.stereotype.Service

@Service
class AdminService(
    private val adminUserRepository: AdminUserRepository
) {
    
    private val passwordEncoder = BCryptPasswordEncoder()
    
    fun authenticate(username: String, password: String): AdminUser? {
        val adminUser = adminUserRepository.findByUsername(username)
        return if (adminUser.isPresent && passwordEncoder.matches(password, adminUser.get().password)) {
            adminUser.get()
        } else {
            null
        }
    }
    
    fun findByUsername(username: String): AdminUser? {
        return adminUserRepository.findByUsername(username).orElse(null)
    }
}
