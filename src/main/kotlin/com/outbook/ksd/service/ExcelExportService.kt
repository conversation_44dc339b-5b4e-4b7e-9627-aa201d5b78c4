package com.outbook.ksd.service

import com.outbook.ksd.entity.SignInRecord
import org.springframework.stereotype.Service
import java.time.format.DateTimeFormatter

@Service
class ExcelExportService {
    
    fun exportSignInRecords(records: List<SignInRecord>): ByteArray {
        val csvBuilder = StringBuilder()

        // 添加BOM以支持中文显示
        csvBuilder.append("\uFEFF")

        // 创建标题行
        val headers = arrayOf("序号", "公司名称", "姓名", "电话", "职位", "签到时间")
        csvBuilder.append(headers.joinToString(",")).append("\n")

        // 填充数据 - 按签到时间倒序排列
        val sortedRecords = records.sortedByDescending { it.createdAt }
        val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

        sortedRecords.forEachIndexed { index, record ->
            val row = arrayOf(
                (index + 1).toString(),
                "\"${record.companyName}\"", // 用引号包围以处理可能的逗号
                "\"${record.name}\"",
                "\"${record.phone}\"",
                "\"${record.position}\"",
                "\"${record.createdAt.format(dateFormatter)}\""
            )
            csvBuilder.append(row.joinToString(",")).append("\n")
        }

        return csvBuilder.toString().toByteArray(Charsets.UTF_8)
    }
}
