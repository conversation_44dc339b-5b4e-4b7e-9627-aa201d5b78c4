package com.outbook.ksd.repository

import com.outbook.ksd.entity.SignInRecord
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SignInRecordRepository : JpaRepository<SignInRecord, Long> {

    @Query("SELECT s FROM SignInRecord s ORDER BY s.createdAt DESC")
    fun findAllOrderByCreatedAtDesc(): List<SignInRecord>

    @Query("SELECT s FROM SignInRecord s WHERE s.meeting.id = :meetingId ORDER BY s.createdAt DESC")
    fun findByMeetingIdOrderByCreatedAtDesc(meetingId: Long): List<SignInRecord>

    @Query("SELECT COUNT(s) FROM SignInRecord s WHERE s.meeting.id = :meetingId")
    fun countByMeetingId(meetingId: Long): Long
}
