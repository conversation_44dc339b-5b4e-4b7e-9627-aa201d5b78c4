package com.outbook.ksd.repository

import com.outbook.ksd.entity.Meeting
import com.outbook.ksd.entity.MeetingStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface MeetingRepository : JpaRepository<Meeting, Long> {
    
    fun findByStatus(status: MeetingStatus): Optional<Meeting>
    
    @Query("""
        SELECT m FROM Meeting m 
        ORDER BY 
            CASE WHEN m.status = 'CURRENT' THEN 0 ELSE 1 END,
            m.createdAt DESC
    """)
    fun findAllOrderByStatusAndCreatedAt(pageable: Pageable): Page<Meeting>
    
    @Query("""
        SELECT m FROM Meeting m 
        ORDER BY 
            CASE WHEN m.status = 'CURRENT' THEN 0 ELSE 1 END,
            m.createdAt DESC
    """)
    fun findAllOrderByStatusAndCreatedAt(): List<Meeting>
}
