package com.outbook.ksd.config

import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import java.nio.file.Paths

@Configuration
class WebConfig : WebMvcConfigurer {
    
    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        // 配置上传文件的静态资源访问
        val uploadPath = Paths.get("src/main/resources/static/uploads/").toAbsolutePath().toString()
        
        registry.addResourceHandler("/uploads/**")
            .addResourceLocations("file:$uploadPath/")
            .setCachePeriod(3600)
        
        // 保持默认的静态资源配置
        registry.addResourceHandler("/**")
            .addResourceLocations("classpath:/static/")
            .setCachePeriod(3600)
    }
}
