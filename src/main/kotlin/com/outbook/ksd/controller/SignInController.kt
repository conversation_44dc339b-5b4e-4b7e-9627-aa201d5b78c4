package com.outbook.ksd.controller

import com.outbook.ksd.dto.SignInRequest
import com.outbook.ksd.service.SignInService
import jakarta.validation.Valid
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import org.springframework.http.ResponseEntity

@Controller
class SignInController(
    private val signInService: SignInService
) {
    
    @GetMapping("/")
    fun showSignInForm(model: Model): String {
        val currentMeeting = signInService.getCurrentMeeting()
        if (currentMeeting == null) {
            model.addAttribute("errorMessage", "暂无进行中的会议")
            model.addAttribute("signInRequest", SignInRequest("", "", "", ""))
            return "signin"
        }

        model.addAttribute("currentMeeting", currentMeeting)
        model.addAttribute("signInRequest", SignInRequest("", "", "", ""))
        return "signin"
    }
    
    @PostMapping("/signin")
    fun submitSignIn(
        @Valid signInRequest: SignInRequest,
        bindingResult: BindingResult,
        redirectAttributes: RedirectAttributes,
        model: Model
    ): String {
        val currentMeeting = signInService.getCurrentMeeting()
        if (currentMeeting == null) {
            redirectAttributes.addFlashAttribute("errorMessage", "暂无进行中的会议，无法签到")
            return "redirect:/"
        }

        if (bindingResult.hasErrors()) {
            model.addAttribute("currentMeeting", currentMeeting)
            return "signin"
        }

        try {
            val result = signInService.signIn(signInRequest)
            if (result != null) {
                redirectAttributes.addFlashAttribute("successMessage", "签到成功！")
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "签到失败，暂无进行中的会议")
            }
        } catch (e: Exception) {
            redirectAttributes.addFlashAttribute("errorMessage", "签到失败，请重试！")
        }

        return "redirect:/"
    }

    @PostMapping("/signin/ajax")
    @ResponseBody
    fun submitSignInAjax(@Valid signInRequest: SignInRequest, bindingResult: BindingResult): ResponseEntity<Map<String, Any>> {
        val response = mutableMapOf<String, Any>()

        // 检查当前会议
        val currentMeeting = signInService.getCurrentMeeting()
        if (currentMeeting == null) {
            response["success"] = false
            response["message"] = "暂无进行中的会议，无法签到"
            return ResponseEntity.ok(response)
        }

        // 检查表单验证错误
        if (bindingResult.hasErrors()) {
            response["success"] = false
            response["message"] = "请填写完整的信息"
            val errors = mutableMapOf<String, String>()
            bindingResult.fieldErrors.forEach { error ->
                errors[error.field] = error.defaultMessage ?: "输入有误"
            }
            response["errors"] = errors
            return ResponseEntity.ok(response)
        }

        try {
            val result = signInService.signIn(signInRequest)
            if (result != null) {
                response["success"] = true
                response["message"] = "签到成功！感谢您的参与。"
                response["meetingName"] = currentMeeting.name
            } else {
                response["success"] = false
                response["message"] = "签到失败，暂无进行中的会议"
            }
        } catch (e: Exception) {
            response["success"] = false
            response["message"] = "签到失败，请重试！"
        }

        return ResponseEntity.ok(response)
    }
}
