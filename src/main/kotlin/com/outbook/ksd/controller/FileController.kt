package com.outbook.ksd.controller

import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import java.io.File
import java.nio.file.Paths

@Controller
@RequestMapping("/files")
class FileController {
    
    @GetMapping("/uploads/meetings/{filename:.+}")
    fun getUploadedFile(@PathVariable filename: String): ResponseEntity<Resource> {
        try {
            val uploadDir = Paths.get("src/main/resources/static/uploads/meetings").toAbsolutePath()
            val file = File(uploadDir.toFile(), filename)
            
            if (!file.exists() || !file.isFile) {
                return ResponseEntity.notFound().build()
            }
            
            val resource = FileSystemResource(file)
            val contentType = when (file.extension.lowercase()) {
                "png" -> MediaType.IMAGE_PNG
                "jpg", "jpeg" -> MediaType.IMAGE_JPEG
                else -> MediaType.APPLICATION_OCTET_STREAM
            }
            
            return ResponseEntity.ok()
                .contentType(contentType)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"${file.name}\"")
                .body(resource)
                
        } catch (e: Exception) {
            return ResponseEntity.notFound().build()
        }
    }
}
