package com.outbook.ksd.controller

import cn.dev33.satoken.annotation.SaCheckLogin
import cn.dev33.satoken.stp.StpUtil
import com.outbook.ksd.dto.LoginRequest
import com.outbook.ksd.service.AdminService
import com.outbook.ksd.service.ExcelExportService
import com.outbook.ksd.service.SignInService
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Controller
@RequestMapping("/admin")
class AdminController(
    private val adminService: AdminService,
    private val signInService: SignInService,
    private val excelExportService: ExcelExportService
) {
    
    @GetMapping("/login")
    fun showLoginForm(model: Model): String {
        if (StpUtil.isLogin()) {
            return "redirect:/admin/dashboard"
        }
        model.addAttribute("loginRequest", LoginRequest("", ""))
        return "admin/login"
    }
    
    @PostMapping("/login")
    fun login(
        @Valid loginRequest: LoginRequest,
        bindingResult: BindingResult,
        redirectAttributes: RedirectAttributes
    ): String {
        if (bindingResult.hasErrors()) {
            return "admin/login"
        }
        
        val adminUser = adminService.authenticate(loginRequest.username, loginRequest.password)
        if (adminUser != null) {
            StpUtil.login(adminUser.id)
            return "redirect:/admin/dashboard"
        } else {
            redirectAttributes.addFlashAttribute("errorMessage", "用户名或密码错误")
            return "redirect:/admin/login"
        }
    }
    
    @GetMapping("/logout")
    fun logout(): String {
        StpUtil.logout()
        return "redirect:/admin/login"
    }
    
    @GetMapping("/dashboard")
    @SaCheckLogin
    fun dashboard(): String {
        return "redirect:/admin/meetings"
    }
    
    @GetMapping("/export")
    @SaCheckLogin
    fun exportCsv(response: HttpServletResponse) {
        val records = signInService.getAllSignInRecords()
        val csvData = excelExportService.exportSignInRecords(records)

        val filename = "签到记录_${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))}.csv"

        response.contentType = "text/csv; charset=UTF-8"
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$filename\"")
        response.outputStream.write(csvData)
        response.outputStream.flush()
    }
}
