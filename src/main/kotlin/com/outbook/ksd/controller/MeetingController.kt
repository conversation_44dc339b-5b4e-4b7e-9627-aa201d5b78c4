package com.outbook.ksd.controller

import cn.dev33.satoken.annotation.SaCheckLogin
import com.outbook.ksd.dto.MeetingRequest
import com.outbook.ksd.entity.MeetingStatus
import com.outbook.ksd.service.ExcelExportService
import com.outbook.ksd.service.FileUploadService
import com.outbook.ksd.service.MeetingService
import com.outbook.ksd.service.SignInService
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.Valid
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Controller
@RequestMapping("/admin/meetings")
@SaCheckLogin
class MeetingController(
    private val meetingService: MeetingService,
    private val signInService: SignInService,
    private val excelExportService: ExcelExportService,
    private val fileUploadService: FileUploadService
) {
    
    @GetMapping
    fun listMeetings(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "30") size: Int,
        model: Model
    ): String {
        val meetingsPage = meetingService.getAllMeetingsWithSignInCount(page, size)
        model.addAttribute("meetingsPage", meetingsPage)
        model.addAttribute("currentPage", page)
        return "admin/meetings/list"
    }
    
    @GetMapping("/create")
    fun showCreateForm(model: Model): String {
        model.addAttribute("meetingRequest", MeetingRequest("", MeetingStatus.INACTIVE))
        model.addAttribute("meetingStatuses", MeetingStatus.values())
        return "admin/meetings/create"
    }
    
    @PostMapping("/create")
    fun createMeeting(
        @Valid @ModelAttribute meetingRequest: MeetingRequest,
        bindingResult: BindingResult,
        redirectAttributes: RedirectAttributes,
        model: Model
    ): String {
        if (bindingResult.hasErrors()) {
            model.addAttribute("meetingStatuses", MeetingStatus.values())
            return "admin/meetings/create"
        }

        try {
            // 处理Logo上传
            val finalLogoUrl = when (meetingRequest.uploadType) {
                "file" -> {
                    if (meetingRequest.logoFile != null && !meetingRequest.logoFile.isEmpty) {
                        fileUploadService.uploadMeetingLogo(meetingRequest.logoFile)
                    } else {
                        null
                    }
                }
                "url" -> meetingRequest.logoUrl?.takeIf { it.isNotBlank() }
                else -> null
            }

            // 创建会议请求对象
            val finalRequest = meetingRequest.copy(logoUrl = finalLogoUrl)
            meetingService.createMeeting(finalRequest)

            redirectAttributes.addFlashAttribute("successMessage", "会议创建成功！")
        } catch (e: IllegalArgumentException) {
            model.addAttribute("meetingStatuses", MeetingStatus.values())
            model.addAttribute("errorMessage", e.message)
            return "admin/meetings/create"
        } catch (e: Exception) {
            redirectAttributes.addFlashAttribute("errorMessage", "会议创建失败：${e.message}")
        }

        return "redirect:/admin/meetings"
    }

    @GetMapping("/{id}/edit")
    fun showEditForm(
        @PathVariable id: Long,
        model: Model
    ): String {
        val meeting = meetingService.findById(id)
        if (meeting == null) {
            model.addAttribute("errorMessage", "会议不存在")
            return "redirect:/admin/meetings"
        }

        val meetingRequest = MeetingRequest(
            name = meeting.name,
            status = meeting.status,
            logoUrl = meeting.logoUrl
        )

        model.addAttribute("meeting", meeting)
        model.addAttribute("meetingRequest", meetingRequest)
        model.addAttribute("meetingStatuses", MeetingStatus.values())
        return "admin/meetings/edit"
    }

    @PostMapping("/{id}/edit")
    fun updateMeeting(
        @PathVariable id: Long,
        @Valid @ModelAttribute meetingRequest: MeetingRequest,
        bindingResult: BindingResult,
        redirectAttributes: RedirectAttributes,
        model: Model
    ): String {
        val meeting = meetingService.findById(id)
        if (meeting == null) {
            redirectAttributes.addFlashAttribute("errorMessage", "会议不存在")
            return "redirect:/admin/meetings"
        }

        if (bindingResult.hasErrors()) {
            model.addAttribute("meeting", meeting)
            model.addAttribute("meetingStatuses", MeetingStatus.values())
            return "admin/meetings/edit"
        }

        try {
            // 处理Logo更新
            val finalLogoUrl = when (meetingRequest.uploadType) {
                "file" -> {
                    if (meetingRequest.logoFile != null && !meetingRequest.logoFile.isEmpty) {
                        // 删除旧的Logo文件（如果是本地文件）
                        meeting.logoUrl?.let { oldUrl ->
                            if (oldUrl.startsWith("/files/uploads/meetings/")) {
                                fileUploadService.deleteFile(oldUrl)
                            }
                        }
                        // 上传新文件
                        fileUploadService.uploadMeetingLogo(meetingRequest.logoFile)
                    } else {
                        // 保持原有Logo
                        meeting.logoUrl
                    }
                }
                "url" -> meetingRequest.logoUrl?.takeIf { it.isNotBlank() }
                else -> meeting.logoUrl
            }

            // 创建更新请求对象
            val finalRequest = meetingRequest.copy(logoUrl = finalLogoUrl)
            val updatedMeeting = meetingService.updateMeeting(id, finalRequest)

            if (updatedMeeting != null) {
                redirectAttributes.addFlashAttribute("successMessage", "会议「${updatedMeeting.name}」更新成功！")
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "会议更新失败")
            }
        } catch (e: IllegalArgumentException) {
            model.addAttribute("meeting", meeting)
            model.addAttribute("meetingStatuses", MeetingStatus.values())
            model.addAttribute("errorMessage", e.message)
            return "admin/meetings/edit"
        } catch (e: Exception) {
            redirectAttributes.addFlashAttribute("errorMessage", "会议更新失败：${e.message}")
        }

        return "redirect:/admin/meetings"
    }

    @PostMapping("/{id}/set-current")
    fun setMeetingAsCurrent(
        @PathVariable id: Long,
        redirectAttributes: RedirectAttributes
    ): String {
        try {
            val meeting = meetingService.setMeetingAsCurrent(id)
            if (meeting != null) {
                redirectAttributes.addFlashAttribute("successMessage", "已将「${meeting.name}」设置为当前会议")
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "会议不存在")
            }
        } catch (e: Exception) {
            redirectAttributes.addFlashAttribute("errorMessage", "操作失败：${e.message}")
        }
        
        return "redirect:/admin/meetings"
    }
    
    @GetMapping("/{id}/signins")
    fun listSignIns(
        @PathVariable id: Long,
        model: Model
    ): String {
        val meeting = meetingService.findById(id)
        if (meeting == null) {
            model.addAttribute("errorMessage", "会议不存在")
            return "redirect:/admin/meetings"
        }
        
        val records = signInService.getSignInRecordsByMeeting(id)
        model.addAttribute("meeting", meeting)
        model.addAttribute("records", records)
        return "admin/meetings/signins"
    }
    
    @GetMapping("/{id}/export")
    fun exportSignIns(
        @PathVariable id: Long,
        response: HttpServletResponse
    ) {
        try {
            val meeting = meetingService.findById(id)
            if (meeting == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "会议不存在")
                return
            }

            val records = signInService.getSignInRecordsByMeeting(id)
            val csvData = excelExportService.exportSignInRecords(records)

            // 生成文件名（避免中文字符问题）
            val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
            val safeFilename = "meeting_${id}_signins_${timestamp}.csv"
            val displayFilename = "${meeting.name}_签到记录_${timestamp}.csv"

            // 设置响应头
            response.contentType = "text/csv; charset=UTF-8"
            response.characterEncoding = "UTF-8"

            // 处理文件名编码，支持中文
            val encodedFilename = URLEncoder.encode(displayFilename, StandardCharsets.UTF_8.toString())
                .replace("+", "%20") // 空格处理

            response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"$safeFilename\"; filename*=UTF-8''$encodedFilename")
            response.setHeader(HttpHeaders.CONTENT_LENGTH, csvData.size.toString())
            response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache")

            // 写入数据
            response.outputStream.use { outputStream ->
                outputStream.write(csvData)
                outputStream.flush()
            }

        } catch (e: Exception) {
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "导出失败: ${e.message}")
        }
    }
}
