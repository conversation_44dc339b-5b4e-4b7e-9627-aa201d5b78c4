-- 创建会议表
CREATE TABLE meeting (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '会议名称',
    status ENUM('CURRENT', 'INACTIVE') NOT NULL DEFAULT 'INACTIVE' COMMENT '会议状态',
    logo_url VARCHAR(500) NULL COMMENT '会议Logo URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '会议表';

-- 修改签到记录表，添加会议关联
ALTER TABLE sign_in_record 
ADD COLUMN meeting_id BIGINT NULL COMMENT '关联会议ID',
ADD CONSTRAINT fk_sign_in_record_meeting 
    FOREIGN KEY (meeting_id) REFERENCES meeting(id) ON DELETE SET NULL;

-- 创建索引
CREATE INDEX idx_meeting_status ON meeting(status);
CREATE INDEX idx_sign_in_record_meeting_id ON sign_in_record(meeting_id);
CREATE INDEX idx_sign_in_record_created_at ON sign_in_record(created_at);

-- 插入一个默认会议
INSERT INTO meeting (name, status) VALUES ('默认会议', 'CURRENT');
