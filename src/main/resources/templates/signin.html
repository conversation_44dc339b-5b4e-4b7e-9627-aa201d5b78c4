<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议签到</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4a5568;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #718096;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background-color: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        
        .alert-error {
            background-color: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        
        .error-message {
            color: #e53e3e;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .admin-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .admin-link a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }
        
        .admin-link a:hover {
            text-decoration: underline;
        }

        .meeting-info {
            text-align: center;
            margin-bottom: 30px;
        }

        .meeting-logo {
            margin-bottom: 20px;
        }

        .meeting-logo img {
            max-width: 200px;
            max-height: 120px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .meeting-name {
            color: #4a5568;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s ease;
        }

        .modal-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .modal-icon.success {
            color: #48bb78;
        }

        .modal-icon.error {
            color: #e53e3e;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #4a5568;
        }

        .modal-message {
            font-size: 16px;
            color: #718096;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .modal-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .modal-button:hover {
            transform: translateY(-2px);
        }

        .modal-button.error {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 加载状态 */
        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 会议Logo和名称 -->
        <div th:if="${currentMeeting}" class="meeting-info">
            <div th:if="${currentMeeting.logoUrl}" class="meeting-logo">
                <img th:src="${currentMeeting.logoUrl}" alt="会议Logo" />
            </div>
            <h1 class="meeting-name" th:text="${currentMeeting.name}">会议名称</h1>
        </div>

        <div th:unless="${currentMeeting}" class="header">
            <h1>会议签到</h1>
            <p>请填写您的信息完成签到</p>
        </div>
        
        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success" th:text="${successMessage}"></div>
        
        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-error" th:text="${errorMessage}"></div>
        
        <form th:action="@{/signin}" th:object="${signInRequest}" method="post">
            <div class="form-group">
                <label for="companyName">公司名称 *</label>
                <input type="text" id="companyName" th:field="*{companyName}" placeholder="请输入公司名称" required>
                <div th:if="${#fields.hasErrors('companyName')}" class="error-message" th:errors="*{companyName}"></div>
            </div>
            
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" th:field="*{name}" placeholder="请输入您的姓名" required>
                <div th:if="${#fields.hasErrors('name')}" class="error-message" th:errors="*{name}"></div>
            </div>
            
            <div class="form-group">
                <label for="phone">电话 *</label>
                <input type="tel" id="phone" th:field="*{phone}" placeholder="请输入手机号码" required>
                <div th:if="${#fields.hasErrors('phone')}" class="error-message" th:errors="*{phone}"></div>
            </div>
            
            <div class="form-group">
                <label for="position">职位 *</label>
                <input type="text" id="position" th:field="*{position}" placeholder="请输入您的职位" required>
                <div th:if="${#fields.hasErrors('position')}" class="error-message" th:errors="*{position}"></div>
            </div>
            
            <button type="submit" class="btn">立即签到</button>
        </form>
        
        <div class="admin-link">
            <a th:href="@{/admin/login}">管理员登录</a>
        </div>
    </div>

    <!-- 弹窗 -->
    <div id="resultModal" class="modal">
        <div class="modal-content">
            <div id="modalIcon" class="modal-icon"></div>
            <div id="modalTitle" class="modal-title"></div>
            <div id="modalMessage" class="modal-message"></div>
            <button id="modalButton" class="modal-button" onclick="closeModal()">确定</button>
        </div>
    </div>

    <script>
        function submitForm(event) {
            event.preventDefault();

            const form = event.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            const formData = new FormData(form);

            // 显示加载状态
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            // 清除之前的错误提示
            document.querySelectorAll('.error-message').forEach(el => el.style.display = 'none');

            fetch('/signin/ajax', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;

                if (data.success) {
                    // 显示成功弹窗
                    showModal('success', '签到成功！', data.message);
                    // 清空表单
                    form.reset();
                } else {
                    // 显示错误弹窗
                    showModal('error', '签到失败', data.message);

                    // 显示字段错误
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const errorEl = document.querySelector(`#${field} + .error-message`);
                            if (errorEl) {
                                errorEl.textContent = data.errors[field];
                                errorEl.style.display = 'block';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // 恢复按钮状态
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;

                console.error('Error:', error);
                showModal('error', '网络错误', '请检查网络连接后重试');
            });
        }

        function showModal(type, title, message) {
            const modal = document.getElementById('resultModal');
            const icon = document.getElementById('modalIcon');
            const titleEl = document.getElementById('modalTitle');
            const messageEl = document.getElementById('modalMessage');
            const button = document.getElementById('modalButton');

            if (type === 'success') {
                icon.textContent = '✅';
                icon.className = 'modal-icon success';
                button.className = 'modal-button';
            } else {
                icon.textContent = '❌';
                icon.className = 'modal-icon error';
                button.className = 'modal-button error';
            }

            titleEl.textContent = title;
            messageEl.textContent = message;
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('resultModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('resultModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 绑定表单提交事件
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', submitForm);
        });
    </script>
</body>
</html>
