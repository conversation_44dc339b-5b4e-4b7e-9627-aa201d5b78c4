<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议管理 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f7fafc;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3182ce;
        }
        
        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .btn-success {
            background-color: #48bb78;
            color: white;
            font-size: 12px;
            padding: 6px 12px;
        }
        
        .btn-success:hover {
            background-color: #38a169;
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background-color: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        
        .alert-error {
            background-color: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h2 {
            color: #4a5568;
            font-size: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px 25px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background-color: #f7fafc;
            color: #4a5568;
            font-weight: 600;
            font-size: 14px;
        }
        
        td {
            color: #2d3748;
            font-size: 14px;
        }
        
        tr:hover {
            background-color: #f7fafc;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-current {
            background-color: #c6f6d5;
            color: #22543d;
        }
        
        .status-inactive {
            background-color: #fed7d7;
            color: #742a2a;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 10px;
        }
        
        .pagination a {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            text-decoration: none;
            color: #4a5568;
        }
        
        .pagination a:hover {
            background-color: #f7fafc;
        }
        
        .pagination .current {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            table {
                min-width: 800px;
            }
            
            th, td {
                padding: 10px 15px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>会议管理</h1>
            <div class="header-actions">
                <a th:href="@{/admin/meetings/create}" class="btn btn-primary">创建会议</a>
                <a th:href="@{/admin/logout}" class="btn btn-secondary">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success" th:text="${successMessage}"></div>
        
        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-error" th:text="${errorMessage}"></div>
        
        <div class="table-container">
            <div class="table-header">
                <h2>会议列表</h2>
            </div>
            
            <div th:if="${meetingsPage.empty}" class="empty-state">
                <h3>暂无会议</h3>
                <p>还没有创建任何会议，请点击"创建会议"开始</p>
            </div>
            
            <table th:if="${!meetingsPage.empty}">
                <thead>
                    <tr>
                        <th>会议名称</th>
                        <th>状态</th>
                        <th>签到人数</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="meeting : ${meetingsPage.content}">
                        <td th:text="${meeting.name}">会议名称</td>
                        <td>
                            <span th:if="${meeting.status.name() == 'CURRENT'}" class="status-badge status-current">当前</span>
                            <span th:if="${meeting.status.name() == 'INACTIVE'}" class="status-badge status-inactive">无效</span>
                        </td>
                        <td>
                            <a th:href="@{/admin/meetings/{id}/signins(id=${meeting.id})}" 
                               th:text="${meeting.signInCount}" 
                               style="color: #667eea; text-decoration: none;">0</a>
                        </td>
                        <td th:text="${#temporals.format(meeting.createdAt, 'yyyy-MM-dd HH:mm')}">创建时间</td>
                        <td>
                            <form th:if="${meeting.status.name() == 'INACTIVE'}" 
                                  th:action="@{/admin/meetings/{id}/set-current(id=${meeting.id})}" 
                                  method="post" style="display: inline;">
                                <button type="submit" class="btn btn-success">设置为当前会议</button>
                            </form>
                            <span th:if="${meeting.status.name() == 'CURRENT'}" class="status-badge status-current">当前会议</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <div th:if="${meetingsPage.totalPages > 1}" class="pagination">
            <a th:if="${meetingsPage.hasPrevious()}" 
               th:href="@{/admin/meetings(page=${currentPage - 1})}">上一页</a>
            
            <span th:each="i : ${#numbers.sequence(0, meetingsPage.totalPages - 1)}">
                <a th:if="${i != currentPage}" 
                   th:href="@{/admin/meetings(page=${i})}" 
                   th:text="${i + 1}">1</a>
                <span th:if="${i == currentPage}" 
                      class="pagination current" 
                      th:text="${i + 1}">1</span>
            </span>
            
            <a th:if="${meetingsPage.hasNext()}" 
               th:href="@{/admin/meetings(page=${currentPage + 1})}">下一页</a>
        </div>
    </div>
</body>
</html>
