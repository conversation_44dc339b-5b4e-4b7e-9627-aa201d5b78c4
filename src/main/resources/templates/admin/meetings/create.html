<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建会议 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4a5568;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #718096;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #718096;
            color: white;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        
        .btn-secondary:hover {
            background: #4a5568;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-error {
            background-color: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        
        .error-message {
            color: #e53e3e;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .form-help {
            font-size: 14px;
            color: #718096;
            margin-top: 5px;
        }

        .upload-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .upload-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #718096;
            font-weight: 500;
        }

        .upload-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .upload-content {
            display: none;
        }

        .upload-content.active {
            display: block;
        }

        .file-upload-area {
            border: 2px dashed #e2e8f0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: border-color 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #667eea;
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background-color: #f7fafc;
        }

        .upload-icon {
            font-size: 48px;
            color: #cbd5e0;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #4a5568;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 14px;
            color: #718096;
        }

        .file-input {
            display: none;
        }

        .preview-container {
            margin-top: 15px;
            text-align: center;
        }

        .preview-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .preview-info {
            margin-top: 10px;
            font-size: 14px;
            color: #718096;
        }

        .remove-file {
            margin-top: 10px;
            background: #e53e3e;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .remove-file:hover {
            background: #c53030;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>创建会议</h1>
            <p>填写会议信息</p>
        </div>
        
        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-error" th:text="${errorMessage}"></div>
        
        <form th:action="@{/admin/meetings/create}" th:object="${meetingRequest}" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="name">会议名称 *</label>
                <input type="text" id="name" th:field="*{name}" placeholder="请输入会议名称" required>
                <div th:if="${#fields.hasErrors('name')}" class="error-message" th:errors="*{name}"></div>
            </div>

            <div class="form-group">
                <label for="status">会议状态</label>
                <select id="status" th:field="*{status}">
                    <option th:each="status : ${meetingStatuses}"
                            th:value="${status}"
                            th:text="${status == T(com.outbook.ksd.entity.MeetingStatus).CURRENT ? '当前' : '无效'}">状态</option>
                </select>
                <div class="form-help">注意：设置为"当前"会将其他会议设为"无效"</div>
            </div>

            <div class="form-group">
                <label>会议Logo (可选)</label>

                <div class="upload-tabs">
                    <div class="upload-tab active" onclick="switchUploadType('file')">上传图片</div>
                    <div class="upload-tab" onclick="switchUploadType('url')">输入URL</div>
                </div>

                <input type="hidden" id="uploadType" name="uploadType" value="file">

                <!-- 文件上传 -->
                <div id="fileUpload" class="upload-content active">
                    <div class="file-upload-area" onclick="document.getElementById('logoFile').click()">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">点击选择图片或拖拽到此处</div>
                        <div class="upload-hint">支持 JPG、JPEG、PNG 格式，最大 5MB</div>
                    </div>
                    <input type="file" id="logoFile" name="logoFile" class="file-input"
                           accept="image/jpeg,image/jpg,image/png" onchange="handleFileSelect(this)">

                    <div id="filePreview" class="preview-container" style="display: none;">
                        <img id="previewImage" class="preview-image" src="" alt="预览">
                        <div id="fileInfo" class="preview-info"></div>
                        <button type="button" class="remove-file" onclick="removeFile()">移除文件</button>
                    </div>
                </div>

                <!-- URL输入 -->
                <div id="urlUpload" class="upload-content">
                    <input type="url" id="logoUrl" th:field="*{logoUrl}" placeholder="https://example.com/logo.png">
                    <div class="form-help">支持 http:// 或 https:// 开头的图片链接</div>
                    <div th:if="${#fields.hasErrors('logoUrl')}" class="error-message" th:errors="*{logoUrl}"></div>
                </div>
            </div>

            <button type="submit" class="btn">创建会议</button>
        </form>
        
        <a th:href="@{/admin/meetings}" class="btn btn-secondary">返回会议列表</a>
    </div>

    <script>
        function switchUploadType(type) {
            // 更新标签页状态
            document.querySelectorAll('.upload-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.upload-content').forEach(content => {
                content.classList.remove('active');
            });

            if (type === 'file') {
                document.getElementById('fileUpload').classList.add('active');
                document.getElementById('uploadType').value = 'file';
            } else {
                document.getElementById('urlUpload').classList.add('active');
                document.getElementById('uploadType').value = 'url';
            }
        }

        function handleFileSelect(input) {
            const file = input.files[0];
            if (!file) return;

            // 验证文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                alert('只支持 JPG、JPEG、PNG 格式的图片');
                input.value = '';
                return;
            }

            // 验证文件大小 (5MB)
            const maxSize = 5 * 1024 * 1024;
            if (file.size > maxSize) {
                alert('文件大小不能超过 5MB');
                input.value = '';
                return;
            }

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('previewImage').src = e.target.result;
                document.getElementById('fileInfo').textContent =
                    `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                document.getElementById('filePreview').style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        function removeFile() {
            document.getElementById('logoFile').value = '';
            document.getElementById('filePreview').style.display = 'none';
        }

        // 拖拽上传功能
        const uploadArea = document.querySelector('.file-upload-area');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('logoFile').files = files;
                handleFileSelect(document.getElementById('logoFile'));
            }
        });
    </script>
</body>
</html>
