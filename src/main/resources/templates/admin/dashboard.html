<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 签到记录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f7fafc;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3182ce;
        }
        
        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .stats {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .stats h2 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .stats-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-header h2 {
            color: #4a5568;
            font-size: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px 25px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background-color: #f7fafc;
            color: #4a5568;
            font-weight: 600;
            font-size: 14px;
        }
        
        td {
            color: #2d3748;
            font-size: 14px;
        }
        
        tr:hover {
            background-color: #f7fafc;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            table {
                min-width: 600px;
            }
            
            th, td {
                padding: 10px 15px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>管理后台</h1>
            <div class="header-actions">
                <a th:href="@{/admin/export}" class="btn btn-primary">导出CSV</a>
                <a th:href="@{/admin/logout}" class="btn btn-secondary">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats">
            <h2>签到统计</h2>
            <div class="stats-number" th:text="${#lists.size(records)}">0</div>
            <p>总签到人数</p>
        </div>
        
        <div class="table-container">
            <div class="table-header">
                <h2>签到记录</h2>
            </div>
            
            <div th:if="${#lists.isEmpty(records)}" class="empty-state">
                <h3>暂无签到记录</h3>
                <p>还没有人签到，请等待用户签到后再查看</p>
            </div>
            
            <table th:if="${!#lists.isEmpty(records)}">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>公司名称</th>
                        <th>姓名</th>
                        <th>电话</th>
                        <th>职位</th>
                        <th>签到时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="record, iterStat : ${records}">
                        <td th:text="${iterStat.count}">1</td>
                        <td th:text="${record.companyName}">公司名称</td>
                        <td th:text="${record.name}">姓名</td>
                        <td th:text="${record.phone}">电话</td>
                        <td th:text="${record.position}">职位</td>
                        <td th:text="${#temporals.format(record.createdAt, 'yyyy-MM-dd HH:mm:ss')}">签到时间</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
