spring.application.name=ksd

# ?????
spring.datasource.url=************************************
#spring.datasource.username=root
#spring.datasource.password=root
spring.datasource.username=ksd
spring.datasource.password=Ksd@2025
spring.datasource.name=ksd
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA??
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Flyway??
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.out-of-order=true
spring.flyway.validate-on-migrate=false

# sa-token??
sa-token.token-name=XMKSD
sa-token.timeout=7200
sa-token.active-timeout=-1
sa-token.is-concurrent=true
sa-token.is-share=false
sa-token.token-style=uuid

# 服务器配置
server.port=8081

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=10MB

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=3600
